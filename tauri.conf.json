{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:5173", "distDir": "./dist", "withGlobalTauri": false}, "package": {"productName": "AI Review", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true, "setFocus": true}, "notification": {"all": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.ai-review.app", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "AI Review", "width": 800, "height": 600, "minWidth": 600, "minHeight": 400, "center": true, "visible": true, "alwaysOnTop": true, "decorations": false, "transparent": false}]}}