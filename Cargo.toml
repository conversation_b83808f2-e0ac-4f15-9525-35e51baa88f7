[package]
name = "ai-review"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "ai-review-ui"
path = "src/main.rs"

[[bin]]
name = "ai-review-cli"
path = "src/cli.rs"

[dependencies]
tauri = { version = "2.0", features = [
  "tray-icon",
  "image-ico",
  "image-png"
] }
serde = { version = "1.0", features = [ "derive" ] }
serde_json = "1.0"
tokio = { version = "1.0", features = [ "full" ] }
anyhow = "1.0"
clap = { version = "4.0", features = [ "derive" ] }
uuid = { version = "1.0", features = [ "v4" ] }
dirs = "5.0"
interprocess = "1.2"
notify-rust = "4.0"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[features]
default = []
