[package]
name = "ai-review"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "ai-review-ui"
path = "src/main.rs"

[[bin]]
name = "ai-review-cli"
path = "src/cli.rs"

[dependencies]
tauri = { version = "1.0", features = [
  "shell-open",
  "window-close",
  "window-hide",
  "window-maximize",
  "window-minimize",
  "window-set-focus",
  "window-show",
  "window-start-dragging",
  "window-unmaximize",
  "window-unminimize",
  "notification-all"
] }
serde = { version = "1.0", features = [ "derive" ] }
serde_json = "1.0"
tokio = { version = "1.0", features = [ "full" ] }
anyhow = "1.0"
clap = { version = "4.0", features = [ "derive" ] }
uuid = { version = "1.0", features = [ "v4" ] }
dirs = "5.0"
interprocess = "1.2"
notify-rust = "4.0"

[build-dependencies]
tauri-build = { version = "1.0", features = [] }

[features]
default = [ "custom-protocol" ]
custom-protocol = [ "tauri/custom-protocol" ]
