# AI Review - Init 指令功能演示

## 🚀 新功能概述

AI Review 客户端现在支持特殊的 `init` 指令！当用户发送 `init` 命令时，系统会自动返回预设的提示词，无需通过UI界面手动回复。

## ✨ 功能特点

### 1. **智能指令识别**
- 自动检测 `init` 命令（不区分大小写）
- 直接返回预设提示词，跳过UI交互流程
- 支持自定义提示词内容

### 2. **可视化设置界面**
- 美观的设置弹窗界面
- 实时预览和编辑提示词
- 一键重置为默认提示词
- 支持键盘快捷键操作

### 3. **持久化配置**
- 自动保存用户自定义的提示词
- 应用重启后配置保持不变
- 配置文件存储在用户目录

## 🎯 使用方法

### 命令行使用
```bash
# 发送 init 指令
./target/release/ai-review-cli "init"

# 系统会立即返回预设的提示词，例如：
✅ 收到回复: 你好！我是AI助手，请告诉我你需要什么帮助。

我可以帮助你：
- 代码审查和优化建议
- 技术问题解答
- 编程最佳实践指导
- 代码重构建议

请描述你的问题或粘贴需要审查的代码。
```

### UI界面设置

1. **打开设置**
   - 点击窗口右上角的设置按钮（⚙️图标）
   - 或使用快捷键打开设置弹窗

2. **编辑提示词**
   - 在文本框中编辑提示词内容
   - 支持多行文本和格式化
   - 最大长度2000字符

3. **保存设置**
   - 点击"保存设置"按钮
   - 或使用 Ctrl/Cmd + Enter 快捷键
   - 系统会自动保存到配置文件

4. **重置默认**
   - 点击"重置为默认"按钮
   - 恢复系统预设的提示词内容

## 🔧 技术实现

### 后端功能
- **指令检测**: 在消息处理流程中检测 `init` 指令
- **配置管理**: 使用 JSON 格式存储配置文件
- **Tauri 命令**: 提供获取、设置、重置提示词的API

### 前端界面
- **Vue 3 组件**: 使用 Composition API 构建设置界面
- **Ant Design Vue**: 提供美观的UI组件
- **响应式设计**: 支持不同窗口大小

### 配置存储
```json
{
  "init_prompt": "你好！我是AI助手，请告诉我你需要什么帮助。\n\n我可以帮助你：\n- 代码审查和优化建议\n- 技术问题解答\n- 编程最佳实践指导\n- 代码重构建议\n\n请描述你的问题或粘贴需要审查的代码。"
}
```

## 📝 使用场景

### 1. **快速获取帮助信息**
```bash
# 用户想了解AI助手的功能
./target/release/ai-review-cli "init"
```

### 2. **自动化脚本集成**
```bash
#!/bin/bash
# 在脚本中获取AI助手的使用说明
HELP_TEXT=$(./target/release/ai-review-cli "init")
echo "$HELP_TEXT"
```

### 3. **团队协作标准化**
- 团队可以自定义统一的提示词模板
- 确保所有成员获得一致的使用指导
- 包含项目特定的代码规范和要求

## 🎨 界面展示

### 主界面
- 新增设置按钮，位于窗口控制按钮旁边
- 蓝色悬停效果，与整体设计风格一致

### 设置弹窗
- 清晰的标题和说明信息
- 大文本框支持多行编辑
- 字符计数和长度限制
- 操作按钮布局合理

### 交互体验
- 支持键盘快捷键操作
- 实时保存和加载配置
- 友好的错误提示和成功反馈

## 🔮 未来扩展

1. **多模板支持**: 支持多个预设模板供用户选择
2. **变量替换**: 支持在提示词中使用动态变量
3. **导入导出**: 支持配置的导入导出功能
4. **团队共享**: 支持团队配置的云端同步

## 📊 测试结果

✅ **指令识别**: `init` 命令正确识别并处理  
✅ **提示词返回**: 默认提示词正确返回  
✅ **配置保存**: 自定义提示词成功保存  
✅ **界面交互**: 设置界面功能正常  
✅ **快捷键**: 键盘快捷键正常工作  
✅ **持久化**: 重启应用后配置保持  

## 🎉 总结

Init 指令功能为 AI Review 应用增加了更强的自动化能力和用户自定义性。用户可以：

- 🚀 **快速获取帮助**: 一条命令即可获取使用指导
- ⚙️ **个性化定制**: 根据需求自定义提示词内容  
- 🎯 **提升效率**: 减少手动操作，提高工作流程效率
- 👥 **团队协作**: 标准化团队的AI助手使用方式

这个功能完美地结合了命令行的便捷性和图形界面的易用性，为用户提供了更好的使用体验。
