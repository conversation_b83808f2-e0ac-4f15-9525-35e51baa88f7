<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Review - 界面测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #ffffff;
            height: 100vh;
            overflow: hidden;
            color: #333;
        }

        .app-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #ffffff;
            overflow: hidden;
            position: relative;
        }

        /* 标题栏 */
        .title-bar {
            height: 50px;
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            user-select: none;
            flex-shrink: 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .title-content {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .app-title {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #1f2937;
            font-weight: 600;
            font-size: 16px;
        }

        .app-icon {
            font-size: 20px;
            color: #3b82f6;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #10b981;
        }

        .status-text {
            color: #6b7280;
            font-size: 13px;
            font-weight: 500;
        }

        .window-controls {
            display: flex;
            gap: 4px;
        }

        .control-btn {
            width: 28px;
            height: 28px;
            border-radius: 6px;
            color: #6b7280;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
            background: none;
        }

        .control-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .control-btn.close:hover {
            background: #ef4444;
            color: white;
        }

        .control-btn.minimize:hover {
            background: #f59e0b;
            color: white;
        }

        .control-btn.settings:hover {
            background: #3b82f6;
            color: white;
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            min-height: 0;
        }

        /* 聊天历史区域 */
        .chat-history {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f9fafb;
            min-height: 200px;
            max-height: 50vh;
        }

        .empty-state {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 16px;
        }

        .empty-icon {
            font-size: 48px;
            color: #9ca3af;
        }

        .empty-description {
            color: #6b7280;
            font-size: 16px;
            text-align: center;
        }

        /* 消息列表 */
        .messages {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message-item {
            display: flex;
            max-width: 80%;
        }

        .message-item.incoming {
            align-self: flex-start;
        }

        .message-item.outgoing {
            align-self: flex-end;
        }

        .message-card {
            width: 100%;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            padding: 12px 16px;
        }

        .message-incoming .message-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
        }

        .message-outgoing .message-card {
            background: #3b82f6;
            border: 1px solid #3b82f6;
        }

        .message-outgoing .message-content {
            color: white;
        }

        .message-outgoing .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        .message-text {
            font-size: 14px;
            line-height: 1.5;
            word-wrap: break-word;
            margin-bottom: 8px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            display: flex;
            align-items: center;
            gap: 4px;
            justify-content: flex-end;
        }

        /* 当前请求区域 */
        .current-request {
            background: #ffffff;
            border-top: 1px solid #e5e7eb;
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 400px;
            max-height: 70vh;
            overflow: hidden;
        }

        .request-card {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            margin: 16px;
        }

        .request-header {
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            border-radius: 12px 12px 0 0;
            padding: 16px 20px;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-icon {
            font-size: 20px;
            color: #3b82f6;
        }

        .header-text h3 {
            margin: 0 0 4px 0;
            color: #1f2937;
            font-size: 16px;
            font-weight: 600;
        }

        .scrollable-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            padding-bottom: 10px;
        }

        .message-section {
            margin-bottom: 20px;
        }

        .message-bubble {
            border-left: 4px solid #3b82f6;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            background: white;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #3b82f6;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .message-content {
            color: #1f2937;
            line-height: 1.6;
            font-size: 14px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .response-section {
            margin-bottom: 0;
        }

        .response-label {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #1f2937;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .response-textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.2s;
        }

        .response-textarea:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .action-footer {
            padding: 20px;
            background: #ffffff;
            border-top: 1px solid #e5e7eb;
            border-radius: 0 0 12px 12px;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: flex-end;
            margin-bottom: 16px;
        }

        .btn {
            min-width: 120px;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid;
        }

        .send-btn {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .send-btn:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .cancel-btn {
            background: white;
            border-color: #d1d5db;
            color: #374151;
        }

        .cancel-btn:hover {
            background: #f9fafb;
            transform: translateY(-1px);
        }

        .shortcuts-hint {
            text-align: center;
        }

        .hint-text {
            color: #6b7280;
            font-size: 13px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 窗口标题栏 -->
        <div class="title-bar">
            <div class="title-content">
                <div class="app-title">
                    <span class="app-icon">🤖</span>
                    <span class="app-name">AI Review</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span class="status-text">已连接</span>
                </div>
            </div>
            <div class="window-controls">
                <button class="control-btn settings" title="设置">⚙️</button>
                <button class="control-btn minimize">−</button>
                <button class="control-btn close">×</button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 聊天历史区域 -->
            <div class="chat-history">
                <div class="messages">
                    <div class="message-item incoming">
                        <div class="message-card">
                            <div class="message-text">这是一个测试消息</div>
                            <div class="message-time">🕐 12:52:54</div>
                        </div>
                    </div>
                    <div class="message-item outgoing">
                        <div class="message-card">
                            <div class="message-text">这是我的回复</div>
                            <div class="message-time">🕐 12:53:10</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 当前请求处理区域 -->
            <div class="current-request">
                <div class="request-card">
                    <div class="request-header">
                        <div class="header-content">
                            <span class="header-icon">📧</span>
                            <div class="header-text">
                                <h3>收到新消息</h3>
                                <span style="color: #6b7280; font-size: 12px;">ID: 16ad7242...</span>
                            </div>
                        </div>
                    </div>

                    <div class="scrollable-content">
                        <div class="message-section">
                            <div class="message-bubble">
                                <div class="message-header">
                                    <span>💬</span>
                                    <span>消息内容</span>
                                </div>
                                <div class="message-content">这是一个测试消息</div>
                            </div>
                        </div>

                        <div class="response-section">
                            <div class="response-label">
                                <span>✏️</span>
                                <span>您的回复</span>
                            </div>
                            <textarea class="response-textarea" placeholder="请输入您的回复..."></textarea>
                        </div>
                    </div>

                    <div class="action-footer">
                        <div class="action-buttons">
                            <button class="btn cancel-btn">取消</button>
                            <button class="btn send-btn">发送回复</button>
                        </div>
                        <div class="shortcuts-hint">
                            <div class="hint-text">💡 快捷键: Ctrl/Cmd + Enter 发送 | Escape 取消</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
